from maix import image, display, app, time, camera
import cv2

# 初始化显示和摄像头
disp = display.Display()
cam = camera.Camera(640, 480, image.Format.FMT_BGR888)

# 二值化阈值
binary_threshold = 60

print(f"显示二值化图像，阈值: {binary_threshold}")
print("白色区域: 像素值 >= {binary_threshold}")
print("黑色区域: 像素值 < {binary_threshold}")

while not app.need_exit():
    # 从摄像头读取图像
    img = cam.read()
    img = image.image2cv(img, ensure_bgr=True, copy=False)
    
    # 转灰度并二值化
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    _, binary = cv2.threshold(gray, binary_threshold, 255, cv2.THRESH_BINARY)
    
    # 转换为BGR格式以便显示
    binary_bgr = cv2.cvtColor(binary, cv2.COLOR_GRAY2BGR)
    
    # 添加信息文本
    cv2.putText(binary_bgr, f"Binary Image (Threshold: {binary_threshold})", (10, 30), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
    
    # 统计白色像素数量
    white_pixels = cv2.countNonZero(binary)
    total_pixels = binary.shape[0] * binary.shape[1]
    white_percentage = (white_pixels / total_pixels) * 100
    
    cv2.putText(binary_bgr, f"White pixels: {white_pixels} ({white_percentage:.1f}%)", (10, 60), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
    cv2.putText(binary_bgr, f"Black pixels: {total_pixels - white_pixels} ({100-white_percentage:.1f}%)", (10, 90), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
    
    # 转回 maix.image.Image 并显示
    img_show = image.cv2image(binary_bgr, bgr=True, copy=False)
    disp.show(img_show)
    
    time.sleep_ms(50)
