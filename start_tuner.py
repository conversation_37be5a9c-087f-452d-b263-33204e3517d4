#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
矩形检测阈值调参工具 - 快速启动脚本

这是一个简化的启动脚本，自动检测环境并启动合适的模式。

使用方法:
    python start_tuner.py

或者直接双击运行 (Windows)
"""

import os
import sys

def main():
    """快速启动主函数"""
    print("🎯 矩形检测阈值调参工具")
    print("🚀 正在启动...")
    
    # 检查主程序是否存在
    main_program = "threshold_tuner_main.py"
    if not os.path.exists(main_program):
        print(f"❌ 找不到主程序文件: {main_program}")
        print("💡 请确保所有文件都在同一目录下")
        input("按回车键退出...")
        return 1
    
    # 启动主程序
    try:
        import subprocess
        result = subprocess.run([sys.executable, main_program], 
                              cwd=os.path.dirname(os.path.abspath(__file__)))
        return result.returncode
    
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
