from maix import image, display, app, time, camera
import cv2

# 初始化显示和摄像头
disp = display.Display()
cam = camera.Camera(640, 480, image.Format.FMT_BGR888)

# 二值化阈值
binary_threshold = 60

print(f"当前二值化阈值: {binary_threshold}")
print("显示模式:")
print("- 左上: 原始图像")
print("- 右上: 灰度图像") 
print("- 左下: 二值化图像")
print("- 右下: 检测结果")

while not app.need_exit():
    # 从摄像头读取图像
    img = cam.read()
    img = image.image2cv(img, ensure_bgr=True, copy=False)
    
    # 转灰度并二值化
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    _, binary = cv2.threshold(gray, binary_threshold, 255, cv2.THRESH_BINARY)
    
    # 查找轮廓进行矩形检测
    contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
    quads = []
    for cnt in contours:
        area = cv2.contourArea(cnt)
        if 30000 < area < 250000:  # 面积阈值
            epsilon = 0.03 * cv2.arcLength(cnt, True)
            approx = cv2.approxPolyDP(cnt, epsilon, True)
            if len(approx) == 4:  # 四边形
                quads.append((approx, area))
    
    # 按面积排序
    quads.sort(key=lambda x: x[1], reverse=True)
    
    # 创建四个显示区域
    h, w = img.shape[:2]
    
    # 1. 原始图像 (左上)
    original = img.copy()
    cv2.putText(original, "Original", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
    
    # 2. 灰度图像 (右上)
    gray_bgr = cv2.cvtColor(gray, cv2.COLOR_GRAY2BGR)
    cv2.putText(gray_bgr, "Grayscale", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
    
    # 3. 二值化图像 (左下)
    binary_bgr = cv2.cvtColor(binary, cv2.COLOR_GRAY2BGR)
    cv2.putText(binary_bgr, f"Binary (T={binary_threshold})", (10, 30), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
    cv2.putText(binary_bgr, f"White pixels: {cv2.countNonZero(binary)}", (10, 60), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
    
    # 4. 检测结果 (右下)
    result = img.copy()
    for i, (approx, area) in enumerate(quads):
        color = (0, 0, 255) if i == 0 else (0, 255, 0)  # 红色为最大，绿色为其他
        cv2.drawContours(result, [approx], -1, color, 3)
        # 显示面积
        if len(approx) > 0:
            x, y = approx[0][0]
            cv2.putText(result, f"{int(area)}", (x, y-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)
    
    cv2.putText(result, f"Detected: {len(quads)} quads", (10, 30), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)
    
    # 缩放图像以适应显示
    scale = 0.5
    new_w, new_h = int(w * scale), int(h * scale)
    
    original_small = cv2.resize(original, (new_w, new_h))
    gray_small = cv2.resize(gray_bgr, (new_w, new_h))
    binary_small = cv2.resize(binary_bgr, (new_w, new_h))
    result_small = cv2.resize(result, (new_w, new_h))
    
    # 拼接四个图像为2x2布局
    top_row = cv2.hconcat([original_small, gray_small])
    bottom_row = cv2.hconcat([binary_small, result_small])
    combined = cv2.vconcat([top_row, bottom_row])
    
    # 添加分割线
    cv2.line(combined, (new_w, 0), (new_w, new_h * 2), (255, 255, 255), 2)  # 垂直线
    cv2.line(combined, (0, new_h), (new_w * 2, new_h), (255, 255, 255), 2)  # 水平线
    
    # 转回 maix.image.Image 并显示
    img_show = image.cv2image(combined, bgr=True, copy=False)
    disp.show(img_show)
    
    time.sleep_ms(50)  # 控制帧率
