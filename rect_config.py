# 矩形检测参数配置文件
class RectDetectionParams:
    """矩形检测参数配置类"""
    
    # 摄像头参数
    CAMERA_WIDTH = 640
    CAMERA_HEIGHT = 480
    
    # 二值化参数
    BINARY_THRESHOLD = 50           # 二值化阈值 (0-255)
    BINARY_MAX_VALUE = 255          # 二值化最大值
    BINARY_TYPE = "THRESH_BINARY"   # 二值化类型
    
    # 轮廓检测参数
    MIN_CONTOUR_AREA = 30000        # 最小轮廓面积
    MAX_CONTOUR_AREA = 250000       # 最大轮廓面积
    TARGET_SIDES = 4                # 目标边数（矩形为4）
    EPSILON_FACTOR = 0.03           # 轮廓近似精度因子 (0.01-0.1)
    
    # 轮廓检索参数
    CONTOUR_MODE = "RETR_TREE"      # 轮廓检索模式
    CONTOUR_METHOD = "CHAIN_APPROX_SIMPLE"  # 轮廓近似方法
    
    # 显示参数
    OUTER_RECT_COLOR = (0, 0, 255)  # 外框颜色 (BGR: 红色)
    INNER_RECT_COLOR = (0, 255, 0)  # 内框颜色 (BGR: 绿色)
    LINE_THICKNESS = 2              # 线条粗细
    
    # 文本显示参数
    FONT = "FONT_HERSHEY_SIMPLEX"   # 字体
    FONT_SCALE = 0.6                # 字体大小
    TEXT_COLOR = (255, 255, 255)    # 文本颜色 (BGR: 白色)
    TEXT_THICKNESS = 2              # 文本粗细
    
    @classmethod
    def get_optimized_params(cls, scene_type="default"):
        """根据场景类型获取优化参数"""
        params = {
            "default": {
                "binary_threshold": 50,
                "epsilon_factor": 0.03,
                "min_area": 30000,
                "max_area": 250000
            },
            "bright": {  # 明亮环境
                "binary_threshold": 80,
                "epsilon_factor": 0.025,
                "min_area": 25000,
                "max_area": 300000
            },
            "dark": {    # 暗光环境
                "binary_threshold": 30,
                "epsilon_factor": 0.035,
                "min_area": 20000,
                "max_area": 200000
            },
            "precise": { # 精确检测
                "binary_threshold": 60,
                "epsilon_factor": 0.015,
                "min_area": 35000,
                "max_area": 250000
            }
        }
        return params.get(scene_type, params["default"])
    
    @classmethod
    def print_current_params(cls):
        """打印当前参数设置"""
        print("=== 矩形检测参数 ===")
        print(f"摄像头分辨率: {cls.CAMERA_WIDTH}x{cls.CAMERA_HEIGHT}")
        print(f"二值化阈值: {cls.BINARY_THRESHOLD}")
        print(f"轮廓面积范围: {cls.MIN_CONTOUR_AREA} - {cls.MAX_CONTOUR_AREA}")
        print(f"轮廓近似精度: {cls.EPSILON_FACTOR}")
        print(f"目标边数: {cls.TARGET_SIDES}")
        print("==================")

# 参数调整建议
ADJUSTMENT_TIPS = {
    "binary_threshold": {
        "description": "二值化阈值调整",
        "increase_when": "背景太亮或矩形太暗时增加",
        "decrease_when": "背景太暗或矩形太亮时减少",
        "range": "0-255",
        "step": 5
    },
    "epsilon_factor": {
        "description": "轮廓近似精度调整", 
        "increase_when": "需要更简化的轮廓时增加",
        "decrease_when": "需要更精确的轮廓时减少",
        "range": "0.01-0.1",
        "step": 0.005
    },
    "contour_area": {
        "description": "轮廓面积范围调整",
        "increase_when": "目标矩形较大时增加",
        "decrease_when": "目标矩形较小时减少",
        "min_step": 5000,
        "max_step": 10000
    }
}

def print_adjustment_tips():
    """打印参数调整建议"""
    print("\n=== 参数调整建议 ===")
    for param, tips in ADJUSTMENT_TIPS.items():
        print(f"\n{tips['description']}:")
        print(f"  增加条件: {tips['increase_when']}")
        print(f"  减少条件: {tips['decrease_when']}")
        if 'range' in tips:
            print(f"  取值范围: {tips['range']}")
        if 'step' in tips:
            print(f"  调整步长: {tips['step']}")
    print("==================\n")
