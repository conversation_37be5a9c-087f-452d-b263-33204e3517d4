#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
矩形检测阈值调参工具 - 交互式界面控制器
支持实时调整cv2.threshold参数，提供可视化反馈和参数保存功能
"""

from maix import image, display, app, time, camera
import cv2
import numpy as np
from rect_config import RectDetectionParams

class ThresholdTuner:
    """阈值调参核心控制器"""
    
    def __init__(self, cam_width=640, cam_height=480):
        """初始化调参工具"""
        # 初始化摄像头和显示器
        self.disp = display.Display()
        self.cam = camera.Camera(cam_width, cam_height, image.Format.FMT_BGR888)
        
        # 当前参数设置
        self.current_threshold = RectDetectionParams.BINARY_THRESHOLD
        self.min_threshold = 0
        self.max_threshold = 255
        self.step_size = 5
        
        # 预设参数
        self.presets = RectDetectionParams.get_optimized_params()
        self.current_preset = "default"
        
        # 界面参数
        self.slider_width = 400
        self.slider_height = 20
        self.slider_x = 120
        self.slider_y = 50
        
        # 显示参数
        self.font = cv2.FONT_HERSHEY_SIMPLEX
        self.font_scale = 0.6
        self.font_thickness = 2
        self.text_color = (255, 255, 255)  # 白色
        self.slider_bg_color = (100, 100, 100)  # 灰色背景
        self.slider_fg_color = (0, 255, 0)  # 绿色前景
        self.slider_handle_color = (0, 0, 255)  # 红色滑块
        
        # 统计信息
        self.white_pixels = 0
        self.total_pixels = 0
        self.white_percentage = 0.0

        # 显示管理器
        self.display_manager = DisplayManager()
        self.display_mode = "dual"  # "dual" 或 "quad"

        # 预设管理器
        self.preset_manager = PresetManager()

        # 代码更新器
        self.code_updater = CodeUpdater()

        print("ThresholdTuner 初始化完成")
        print(f"当前阈值: {self.current_threshold}")

        # 显示可用预设
        self.preset_manager.list_all_presets()
    
    def draw_slider(self, img, value, min_val, max_val, x, y, width, height):
        """绘制虚拟滑动条"""
        # 计算滑块位置
        normalized_value = (value - min_val) / (max_val - min_val)
        handle_x = int(x + normalized_value * width)
        
        # 绘制滑动条背景
        cv2.rectangle(img, (x, y), (x + width, y + height), self.slider_bg_color, -1)
        
        # 绘制滑动条前景（已填充部分）
        cv2.rectangle(img, (x, y), (handle_x, y + height), self.slider_fg_color, -1)
        
        # 绘制滑块手柄
        handle_size = height + 4
        cv2.rectangle(img, 
                     (handle_x - handle_size//2, y - 2), 
                     (handle_x + handle_size//2, y + height + 2), 
                     self.slider_handle_color, -1)
        
        # 绘制边框
        cv2.rectangle(img, (x, y), (x + width, y + height), (255, 255, 255), 1)
        
        return handle_x
    
    def draw_interface(self, img):
        """绘制用户界面元素"""
        h, w = img.shape[:2]
        
        # 绘制标题
        cv2.putText(img, "Threshold Tuner", (10, 30), 
                   self.font, 0.8, self.text_color, self.font_thickness)
        
        # 绘制当前阈值
        cv2.putText(img, f"Threshold: {self.current_threshold}", (10, self.slider_y - 10), 
                   self.font, self.font_scale, self.text_color, self.font_thickness)
        
        # 绘制滑动条
        handle_x = self.draw_slider(img, self.current_threshold, 
                                   self.min_threshold, self.max_threshold,
                                   self.slider_x, self.slider_y, 
                                   self.slider_width, self.slider_height)
        
        # 绘制范围标签
        cv2.putText(img, f"{self.min_threshold}", (self.slider_x - 30, self.slider_y + 15), 
                   self.font, 0.4, self.text_color, 1)
        cv2.putText(img, f"{self.max_threshold}", (self.slider_x + self.slider_width + 10, self.slider_y + 15), 
                   self.font, 0.4, self.text_color, 1)
        
        # 绘制像素统计信息
        stats_y = self.slider_y + 50
        cv2.putText(img, f"White pixels: {self.white_pixels} ({self.white_percentage:.1f}%)", 
                   (10, stats_y), self.font, 0.5, self.text_color, 1)
        cv2.putText(img, f"Total pixels: {self.total_pixels}", 
                   (10, stats_y + 25), self.font, 0.5, self.text_color, 1)
        
        # 绘制当前预设信息
        preset_info = self.get_current_preset_info()
        if preset_info:
            preset_text = f"Preset: {preset_info['name']} ({preset_info['type']})"
            cv2.putText(img, preset_text, (10, stats_y + 50), self.font, 0.5, self.text_color, 1)
            # 显示预设描述（截断以适应显示）
            desc = preset_info['description'][:40] + "..." if len(preset_info['description']) > 40 else preset_info['description']
            cv2.putText(img, desc, (10, stats_y + 70), self.font, 0.4, (200, 200, 200), 1)
        else:
            cv2.putText(img, "Preset: Manual", (10, stats_y + 50), self.font, 0.5, self.text_color, 1)
        
        # 绘制操作提示
        help_y = h - 140
        help_texts = [
            "Controls:",
            "Q/W: +/-5   A/S: +/-1   Z/X: +/-10",
            "1-4: Presets   5: List   C: Create",
            "I: Info   E: Export   U: Update",
            "B: Backups   T: Toggle   ESC: Exit"
        ]
        
        for i, text in enumerate(help_texts):
            cv2.putText(img, text, (10, help_y + i * 20), 
                       self.font, 0.4, (200, 200, 200), 1)
        
        return img
    
    def update_statistics(self, binary_img):
        """更新像素统计信息"""
        self.white_pixels = cv2.countNonZero(binary_img)
        self.total_pixels = binary_img.shape[0] * binary_img.shape[1]
        self.white_percentage = (self.white_pixels / self.total_pixels) * 100 if self.total_pixels > 0 else 0
    
    def adjust_threshold(self, delta):
        """调整阈值"""
        new_threshold = self.current_threshold + delta
        self.current_threshold = max(self.min_threshold, min(self.max_threshold, new_threshold))
        print(f"阈值调整为: {self.current_threshold}")
    
    def set_threshold(self, value):
        """直接设置阈值"""
        self.current_threshold = max(self.min_threshold, min(self.max_threshold, value))
        print(f"阈值设置为: {self.current_threshold}")
    
    def apply_preset(self, preset_name):
        """应用预设参数"""
        return self.preset_manager.apply_preset(preset_name, self)
    
    def reset_to_default(self):
        """重置到默认值"""
        self.current_threshold = RectDetectionParams.BINARY_THRESHOLD
        self.current_preset = "default"
        print(f"重置到默认值: {self.current_threshold}")
    
    def save_current_config(self, filename="tuned_config.json"):
        """保存当前配置"""
        config = {
            "binary_threshold": self.current_threshold,
            "preset_used": self.current_preset,
            "timestamp": time.ticks_ms()
        }
        
        success = RectDetectionParams.save_config(filename, config)
        if success:
            print(f"配置已保存到: {filename}")
        return success
    
    def handle_key_input(self, key_char):
        """处理键盘输入"""
        if key_char == 'q':  # +5
            self.adjust_threshold(5)
        elif key_char == 'w':  # -5
            self.adjust_threshold(-5)
        elif key_char == 'a':  # +1
            self.adjust_threshold(1)
        elif key_char == 's':  # -1
            self.adjust_threshold(-1)
        elif key_char == 'z':  # +10
            self.adjust_threshold(10)
        elif key_char == 'x':  # -10
            self.adjust_threshold(-10)
        elif key_char == '1':  # 默认预设
            self.apply_preset("default")
        elif key_char == '2':  # 明亮环境
            self.apply_preset("bright")
        elif key_char == '3':  # 暗光环境
            self.apply_preset("dark")
        elif key_char == '4':  # 精确检测
            self.apply_preset("precise")
        elif key_char == '5':  # 列出预设
            self.list_presets()
        elif key_char == 'r':  # 重置
            self.reset_to_default()
        elif key_char == ' ':  # 保存配置
            self.save_current_config()
        elif key_char == 'c':  # 创建自定义预设
            preset_name = f"custom_{time.ticks_ms() if hasattr(time, 'ticks_ms') else 'preset'}"
            self.create_custom_preset(preset_name, f"自定义预设 - 阈值{self.current_threshold}")
        elif key_char == 'i':  # 显示当前预设信息
            info = self.get_current_preset_info()
            if info:
                print(f"当前预设: {info['name']} - {info['description']}")
                print(f"参数: {info['params']}")
            else:
                print("当前未使用预设")
        elif key_char == 'e':  # 导出参数
            self.export_current_params("all")
        elif key_char == 'u':  # 更新main.py
            print("准备更新main.py中的阈值参数...")
            self.update_main_py(confirm=True)
        elif key_char == 'b':  # 列出备份
            self.list_backups()
        elif key_char == 't':  # 切换显示模式
            self.switch_display_mode()
        elif key_char == '\x1b':  # ESC键退出
            return False
        
        return True
    
    def get_current_threshold(self):
        """获取当前阈值"""
        return self.current_threshold
    
    def process_and_display(self, img):
        """处理图像并创建显示画面"""
        # 转换为灰度图
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # 二值化处理
        _, binary = cv2.threshold(gray, self.current_threshold, 255, cv2.THRESH_BINARY)

        # 更新统计信息
        self.update_statistics(binary)

        # 查找轮廓用于检测显示
        contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

        # 创建检测结果图像
        detection_img, detected_count = self.display_manager.create_detection_overlay(img, contours, self.current_threshold)

        # 准备统计信息字典
        stats_dict = {
            "white_pixels": self.white_pixels,
            "total_pixels": self.total_pixels,
            "white_percentage": self.white_percentage,
            "detected_rects": detected_count
        }

        # 根据显示模式创建画面
        if self.display_mode == "dual":
            display_img = self.display_manager.create_dual_view(img, binary, self.current_threshold, stats_dict)
        else:  # quad mode
            display_img = self.display_manager.create_quad_view(img, gray, binary, detection_img, self.current_threshold, stats_dict)

        # 在显示图像上添加控制界面
        display_img = self.draw_interface(display_img)

        return display_img, binary, detection_img

    def switch_display_mode(self):
        """切换显示模式"""
        self.display_mode = "quad" if self.display_mode == "dual" else "dual"
        print(f"切换到 {'四画面' if self.display_mode == 'quad' else '双画面'} 显示模式")

    def create_custom_preset(self, name, description=""):
        """从当前参数创建自定义预设"""
        return self.preset_manager.create_preset_from_current(name, self, description)

    def delete_custom_preset(self, name):
        """删除自定义预设"""
        return self.preset_manager.delete_custom_preset(name)

    def list_presets(self):
        """列出所有可用预设"""
        self.preset_manager.list_all_presets()

    def get_preset_info(self, preset_name):
        """获取预设详细信息"""
        return self.preset_manager.get_preset_info(preset_name)

    def get_current_preset_info(self):
        """获取当前预设信息"""
        if self.current_preset:
            return self.get_preset_info(self.current_preset)
        return None

    def export_current_params(self, format="all"):
        """导出当前参数"""
        stats = {
            "white_pixels": self.white_pixels,
            "total_pixels": self.total_pixels,
            "white_percentage": self.white_percentage
        }

        if format == "console":
            self.code_updater.export_params_console(self.current_threshold, self.current_preset, stats)
        elif format == "json":
            return self.code_updater.export_params_json(self.current_threshold, "optimized_params.json", self.current_preset, stats)
        elif format == "code":
            return self.code_updater.export_params_code(self.current_threshold)
        elif format == "all":
            return self.code_updater.export_all_formats(self.current_threshold, self.current_preset, stats)

        return True

    def update_main_py(self, confirm=True):
        """更新main.py中的阈值参数"""
        return self.code_updater.update_code_file("main.py", self.current_threshold, confirm)

    def update_test_py(self, confirm=True):
        """更新test.py中的阈值参数"""
        return self.code_updater.update_code_file("test.py", self.current_threshold, confirm)

    def list_backups(self):
        """列出备份文件"""
        return self.code_updater.list_backups()

    def restore_backup(self, filepath, backup_path):
        """从备份恢复文件"""
        return self.code_updater.restore_from_backup(filepath, backup_path)

    def cleanup(self):
        """清理资源"""
        print("ThresholdTuner 清理完成")


class DisplayManager:
    """双画面显示管理器"""

    def __init__(self, scale_factor=0.8):
        """初始化显示管理器"""
        self.scale_factor = scale_factor  # 图像缩放因子
        self.font = cv2.FONT_HERSHEY_SIMPLEX
        self.font_scale = 0.6
        self.font_thickness = 2
        self.title_color = (255, 255, 255)  # 白色标题
        self.info_color = (0, 255, 0)  # 绿色信息
        self.border_color = (255, 255, 255)  # 白色边框
        self.border_thickness = 2

    def add_info_overlay(self, img, title, info_dict=None):
        """在图像上添加标题和信息叠加"""
        img_with_info = img.copy()

        # 添加标题
        cv2.putText(img_with_info, title, (10, 30),
                   self.font, self.font_scale, self.title_color, self.font_thickness)

        # 添加信息
        if info_dict:
            y_offset = 60
            for key, value in info_dict.items():
                info_text = f"{key}: {value}"
                cv2.putText(img_with_info, info_text, (10, y_offset),
                           self.font, 0.5, self.info_color, 1)
                y_offset += 25

        return img_with_info

    def create_dual_view(self, original_img, binary_img, threshold, stats_dict=None):
        """创建原图和二值化图像的双画面显示"""
        h, w = original_img.shape[:2]

        # 计算缩放后的尺寸
        new_w = int(w * self.scale_factor)
        new_h = int(h * self.scale_factor)

        # 缩放图像
        original_resized = cv2.resize(original_img, (new_w, new_h))

        # 处理二值化图像（转换为BGR格式）
        if len(binary_img.shape) == 2:  # 灰度图
            binary_bgr = cv2.cvtColor(binary_img, cv2.COLOR_GRAY2BGR)
        else:
            binary_bgr = binary_img.copy()

        binary_resized = cv2.resize(binary_bgr, (new_w, new_h))

        # 准备信息字典
        original_info = stats_dict.copy() if stats_dict else {}
        binary_info = {
            "Threshold": threshold,
            "White pixels": stats_dict.get("white_pixels", 0) if stats_dict else 0,
            "White %": f"{stats_dict.get('white_percentage', 0):.1f}%" if stats_dict else "0.0%"
        }

        # 添加信息叠加
        left_panel = self.add_info_overlay(original_resized, "Original Image", original_info)
        right_panel = self.add_info_overlay(binary_resized, f"Binary (T={threshold})", binary_info)

        # 水平拼接
        combined = cv2.hconcat([left_panel, right_panel])

        # 添加分割线
        cv2.line(combined, (new_w, 0), (new_w, new_h), self.border_color, self.border_thickness)

        return combined

    def create_quad_view(self, original_img, gray_img, binary_img, result_img, threshold, stats_dict=None):
        """创建四画面显示（原图、灰度图、二值化图、检测结果）"""
        h, w = original_img.shape[:2]

        # 计算缩放后的尺寸（四画面需要更小的缩放）
        quad_scale = self.scale_factor * 0.6
        new_w = int(w * quad_scale)
        new_h = int(h * quad_scale)

        # 缩放所有图像
        original_small = cv2.resize(original_img, (new_w, new_h))
        gray_small = cv2.resize(cv2.cvtColor(gray_img, cv2.COLOR_GRAY2BGR) if len(gray_img.shape) == 2 else gray_img, (new_w, new_h))
        binary_small = cv2.resize(cv2.cvtColor(binary_img, cv2.COLOR_GRAY2BGR) if len(binary_img.shape) == 2 else binary_img, (new_w, new_h))
        result_small = cv2.resize(result_img, (new_w, new_h))

        # 添加标题
        cv2.putText(original_small, "Original", (5, 20), self.font, 0.4, self.title_color, 1)
        cv2.putText(gray_small, "Grayscale", (5, 20), self.font, 0.4, self.title_color, 1)
        cv2.putText(binary_small, f"Binary (T={threshold})", (5, 20), self.font, 0.4, self.info_color, 1)
        cv2.putText(result_small, "Detection", (5, 20), self.font, 0.4, self.title_color, 1)

        # 在二值化图像上添加统计信息
        if stats_dict:
            cv2.putText(binary_small, f"White: {stats_dict.get('white_pixels', 0)}", (5, 40),
                       self.font, 0.3, self.info_color, 1)
            cv2.putText(binary_small, f"{stats_dict.get('white_percentage', 0):.1f}%", (5, 55),
                       self.font, 0.3, self.info_color, 1)

        # 拼接为2x2布局
        top_row = cv2.hconcat([original_small, gray_small])
        bottom_row = cv2.hconcat([binary_small, result_small])
        combined = cv2.vconcat([top_row, bottom_row])

        # 添加分割线
        cv2.line(combined, (new_w, 0), (new_w, new_h * 2), self.border_color, 1)  # 垂直线
        cv2.line(combined, (0, new_h), (new_w * 2, new_h), self.border_color, 1)  # 水平线

        return combined

    def create_detection_overlay(self, img, contours, threshold_value):
        """在图像上绘制矩形检测结果"""
        result_img = img.copy()

        # 矩形检测参数
        min_area = 1000  # 最小面积
        max_area = 50000  # 最大面积

        # 查找并绘制矩形
        quads = []
        for cnt in contours:
            area = cv2.contourArea(cnt)
            if min_area < area < max_area:
                epsilon = 0.03 * cv2.arcLength(cnt, True)
                approx = cv2.approxPolyDP(cnt, epsilon, True)
                if len(approx) == 4:  # 四边形
                    quads.append((approx, area))

        # 按面积排序
        quads.sort(key=lambda x: x[1], reverse=True)

        # 绘制检测到的矩形
        for i, (approx, area) in enumerate(quads):
            color = (0, 0, 255) if i == 0 else (0, 255, 0)  # 最大的用红色，其他用绿色
            cv2.drawContours(result_img, [approx], -1, color, 2)

            # 显示面积信息
            if len(approx) > 0:
                x, y = approx[0][0]
                cv2.putText(result_img, f"{int(area)}", (x, y-10),
                           self.font, 0.4, color, 1)

        # 显示检测统计
        cv2.putText(result_img, f"Detected: {len(quads)} rects", (10, 50),
                   self.font, 0.5, self.title_color, 1)

        return result_img, len(quads)

    def optimize_display_size(self, img_width, img_height, max_display_width=1200, max_display_height=800):
        """优化显示尺寸以适应屏幕"""
        # 计算最佳缩放因子
        width_scale = max_display_width / (img_width * 2)  # 双画面宽度
        height_scale = max_display_height / img_height

        optimal_scale = min(width_scale, height_scale, 1.0)  # 不超过原始尺寸
        self.scale_factor = optimal_scale

        return optimal_scale


class PresetManager:
    """预设参数管理器"""

    def __init__(self):
        """初始化预设管理器"""
        # 加载系统预设
        self.system_presets = RectDetectionParams.get_optimized_params()

        # 自定义预设
        self.custom_presets = {}
        self.custom_presets_file = "custom_presets.json"

        # 预设描述
        self.preset_descriptions = {
            "default": "默认设置 - 通用场景",
            "bright": "明亮环境 - 强光照条件",
            "dark": "暗光环境 - 低光照条件",
            "precise": "精确检测 - 高精度要求"
        }

        # 加载自定义预设
        self.load_custom_presets()

        print("PresetManager 初始化完成")
        print(f"系统预设: {list(self.system_presets.keys())}")
        print(f"自定义预设: {list(self.custom_presets.keys())}")

    def get_all_presets(self):
        """获取所有预设（系统+自定义）"""
        all_presets = self.system_presets.copy()
        all_presets.update(self.custom_presets)
        return all_presets

    def get_preset_list(self):
        """获取预设名称列表"""
        return list(self.system_presets.keys()) + list(self.custom_presets.keys())

    def get_preset_params(self, preset_name):
        """获取指定预设的参数"""
        if preset_name in self.system_presets:
            return self.system_presets[preset_name].copy()
        elif preset_name in self.custom_presets:
            return self.custom_presets[preset_name].copy()
        else:
            return None

    def get_preset_description(self, preset_name):
        """获取预设描述"""
        if preset_name in self.preset_descriptions:
            return self.preset_descriptions[preset_name]
        elif preset_name in self.custom_presets:
            return f"自定义预设 - {preset_name}"
        else:
            return "未知预设"

    def apply_preset(self, preset_name, tuner_instance):
        """应用预设参数到调参器实例"""
        params = self.get_preset_params(preset_name)
        if params is None:
            print(f"预设 '{preset_name}' 不存在")
            return False

        # 应用二值化阈值
        if 'binary_threshold' in params:
            tuner_instance.current_threshold = params['binary_threshold']

        # 更新当前预设名称
        tuner_instance.current_preset = preset_name

        print(f"应用预设: {preset_name}")
        print(f"  描述: {self.get_preset_description(preset_name)}")
        print(f"  阈值: {params.get('binary_threshold', '未设置')}")

        return True

    def save_custom_preset(self, name, params, description=""):
        """保存自定义预设"""
        if not isinstance(params, dict):
            print("参数必须是字典格式")
            return False

        # 验证必要参数
        if 'binary_threshold' not in params:
            print("预设必须包含 binary_threshold 参数")
            return False

        # 保存预设
        self.custom_presets[name] = params.copy()

        # 保存描述
        if description:
            self.preset_descriptions[name] = description

        # 保存到文件
        success = self.save_custom_presets_to_file()

        if success:
            print(f"自定义预设 '{name}' 保存成功")
        else:
            print(f"自定义预设 '{name}' 保存失败")

        return success

    def delete_custom_preset(self, name):
        """删除自定义预设"""
        if name in self.custom_presets:
            del self.custom_presets[name]
            if name in self.preset_descriptions:
                del self.preset_descriptions[name]

            success = self.save_custom_presets_to_file()
            if success:
                print(f"自定义预设 '{name}' 删除成功")
            return success
        else:
            print(f"自定义预设 '{name}' 不存在")
            return False

    def load_custom_presets(self):
        """从文件加载自定义预设"""
        try:
            custom_data = RectDetectionParams.load_config(self.custom_presets_file)
            if custom_data:
                self.custom_presets = custom_data.get('presets', {})
                self.preset_descriptions.update(custom_data.get('descriptions', {}))
                print(f"加载了 {len(self.custom_presets)} 个自定义预设")
            return True
        except Exception as e:
            print(f"加载自定义预设失败: {e}")
            return False

    def save_custom_presets_to_file(self):
        """保存自定义预设到文件"""
        try:
            custom_data = {
                'presets': self.custom_presets,
                'descriptions': {k: v for k, v in self.preset_descriptions.items()
                               if k in self.custom_presets}
            }

            success = RectDetectionParams.save_config(self.custom_presets_file, custom_data)
            return success
        except Exception as e:
            print(f"保存自定义预设失败: {e}")
            return False

    def create_preset_from_current(self, name, tuner_instance, description=""):
        """从当前参数创建预设"""
        current_params = {
            'binary_threshold': tuner_instance.current_threshold,
            'timestamp': time.ticks_ms() if hasattr(time, 'ticks_ms') else 0
        }

        return self.save_custom_preset(name, current_params, description)

    def get_preset_info(self, preset_name):
        """获取预设的详细信息"""
        params = self.get_preset_params(preset_name)
        if params is None:
            return None

        info = {
            'name': preset_name,
            'description': self.get_preset_description(preset_name),
            'type': 'system' if preset_name in self.system_presets else 'custom',
            'params': params
        }

        return info

    def list_all_presets(self):
        """列出所有预设的信息"""
        print("\n=== 可用预设 ===")

        # 系统预设
        print("\n系统预设:")
        for name in self.system_presets.keys():
            params = self.system_presets[name]
            desc = self.get_preset_description(name)
            print(f"  {name}: {desc} (阈值: {params.get('binary_threshold', 'N/A')})")

        # 自定义预设
        if self.custom_presets:
            print("\n自定义预设:")
            for name in self.custom_presets.keys():
                params = self.custom_presets[name]
                desc = self.get_preset_description(name)
                print(f"  {name}: {desc} (阈值: {params.get('binary_threshold', 'N/A')})")

        print("==================\n")


class CodeUpdater:
    """代码更新和参数输出管理器"""

    def __init__(self):
        """初始化代码更新器"""
        self.backup_dir = "backups"
        self.target_files = ["main.py", "test.py"]  # 可能需要更新的文件
        self.backup_suffix = "_backup"

        # 确保备份目录存在
        import os
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)

        print("CodeUpdater 初始化完成")

    def export_params_console(self, threshold, preset_name=None, stats=None):
        """输出参数到控制台"""
        print("\n" + "="*50)
        print("📊 参数调优结果")
        print("="*50)
        print(f"🎯 最优二值化阈值: {threshold}")

        if preset_name:
            print(f"🔧 使用预设: {preset_name}")

        if stats:
            print(f"📈 白色像素: {stats.get('white_pixels', 'N/A')}")
            print(f"📊 白色像素占比: {stats.get('white_percentage', 'N/A'):.1f}%")
            print(f"🔍 检测到矩形: {stats.get('detected_rects', 'N/A')} 个")

        print(f"⏰ 导出时间: {time.ticks_ms() if hasattr(time, 'ticks_ms') else 'N/A'}")
        print("="*50)

    def export_params_json(self, threshold, filename="optimized_params.json", preset_name=None, stats=None):
        """输出参数到JSON文件"""
        export_data = {
            "optimized_params": {
                "binary_threshold": threshold,
                "preset_used": preset_name,
                "optimization_timestamp": time.ticks_ms() if hasattr(time, 'ticks_ms') else 0
            },
            "statistics": stats if stats else {},
            "export_info": {
                "export_time": time.ticks_ms() if hasattr(time, 'ticks_ms') else 0,
                "tool_version": "1.0.0"
            }
        }

        success = RectDetectionParams.save_config(filename, export_data)
        if success:
            print(f"✅ 参数已导出到: {filename}")
        else:
            print(f"❌ 参数导出失败: {filename}")

        return success

    def export_params_code(self, threshold, filename="optimized_params.py"):
        """输出参数为Python代码格式"""
        code_content = f'''# 优化后的矩形检测参数
# 生成时间: {time.ticks_ms() if hasattr(time, 'ticks_ms') else 'N/A'}

# 二值化参数
OPTIMIZED_BINARY_THRESHOLD = {threshold}

# 使用示例:
# _, binary = cv2.threshold(gray, OPTIMIZED_BINARY_THRESHOLD, 255, cv2.THRESH_BINARY)
'''

        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(code_content)
            print(f"✅ 代码格式参数已导出到: {filename}")
            return True
        except Exception as e:
            print(f"❌ 代码格式导出失败: {e}")
            return False

    def create_backup(self, filepath):
        """创建文件备份"""
        import os
        import shutil
        from datetime import datetime

        if not os.path.exists(filepath):
            print(f"❌ 目标文件不存在: {filepath}")
            return False

        try:
            # 生成备份文件名
            filename = os.path.basename(filepath)
            name, ext = os.path.splitext(filename)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"{name}_{timestamp}{self.backup_suffix}{ext}"
            backup_path = os.path.join(self.backup_dir, backup_filename)

            # 创建备份
            shutil.copy2(filepath, backup_path)
            print(f"✅ 备份创建成功: {backup_path}")
            return backup_path

        except Exception as e:
            print(f"❌ 备份创建失败: {e}")
            return False

    def find_threshold_pattern(self, content):
        """查找代码中的阈值模式"""
        import re

        # 匹配 cv2.threshold(gray, 数字, 255, cv2.THRESH_BINARY) 模式
        patterns = [
            r'cv2\.threshold\s*\(\s*gray\s*,\s*(\d+)\s*,\s*255\s*,\s*cv2\.THRESH_BINARY\s*\)',
            r'cv2\.threshold\s*\(\s*\w+\s*,\s*(\d+)\s*,\s*255\s*,\s*cv2\.THRESH_BINARY\s*\)',
            r'binary_threshold\s*=\s*(\d+)',
            r'BINARY_THRESHOLD\s*=\s*(\d+)'
        ]

        matches = []
        for pattern in patterns:
            for match in re.finditer(pattern, content):
                matches.append({
                    'pattern': pattern,
                    'match': match.group(0),
                    'threshold': int(match.group(1)),
                    'start': match.start(),
                    'end': match.end(),
                    'line_num': content[:match.start()].count('\n') + 1
                })

        return matches

    def update_code_file(self, filepath, new_threshold, confirm=True):
        """更新代码文件中的阈值参数"""
        import re

        # 1. 创建备份
        backup_path = self.create_backup(filepath)
        if not backup_path:
            return False

        try:
            # 2. 读取文件内容
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()

            # 3. 查找阈值模式
            matches = self.find_threshold_pattern(content)

            if not matches:
                print(f"❌ 在 {filepath} 中未找到阈值参数")
                return False

            print(f"🔍 在 {filepath} 中找到 {len(matches)} 个阈值参数:")
            for i, match in enumerate(matches):
                print(f"  {i+1}. 第{match['line_num']}行: {match['match']} (当前值: {match['threshold']})")

            # 4. 用户确认
            if confirm:
                print(f"\n⚠️  即将将所有阈值更新为: {new_threshold}")
                print(f"📁 备份文件: {backup_path}")
                response = input("确认更新? (y/N): ").strip().lower()
                if response != 'y':
                    print("❌ 用户取消更新")
                    return False

            # 5. 执行替换
            updated_content = content
            replacement_count = 0

            # 按照位置从后往前替换，避免位置偏移
            for match in sorted(matches, key=lambda x: x['start'], reverse=True):
                old_text = match['match']
                # 替换其中的数字
                new_text = re.sub(r'\d+', str(new_threshold), old_text)
                updated_content = updated_content[:match['start']] + new_text + updated_content[match['end']:]
                replacement_count += 1

            # 6. 写入更新后的内容
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(updated_content)

            print(f"✅ 成功更新 {replacement_count} 个阈值参数")
            print(f"📝 文件已更新: {filepath}")
            return True

        except Exception as e:
            print(f"❌ 更新文件失败: {e}")
            # 尝试恢复备份
            if backup_path:
                try:
                    import shutil
                    shutil.copy2(backup_path, filepath)
                    print(f"🔄 已从备份恢复: {filepath}")
                except:
                    print(f"❌ 备份恢复也失败了!")
            return False

    def restore_from_backup(self, filepath, backup_path=None):
        """从备份恢复文件"""
        import os
        import shutil

        if backup_path and os.path.exists(backup_path):
            try:
                shutil.copy2(backup_path, filepath)
                print(f"✅ 已从备份恢复: {filepath}")
                return True
            except Exception as e:
                print(f"❌ 恢复失败: {e}")
                return False
        else:
            print(f"❌ 备份文件不存在: {backup_path}")
            return False

    def list_backups(self):
        """列出所有备份文件"""
        import os

        if not os.path.exists(self.backup_dir):
            print("📁 备份目录不存在")
            return []

        backups = []
        for filename in os.listdir(self.backup_dir):
            if self.backup_suffix in filename:
                filepath = os.path.join(self.backup_dir, filename)
                stat = os.stat(filepath)
                backups.append({
                    'filename': filename,
                    'path': filepath,
                    'size': stat.st_size,
                    'mtime': stat.st_mtime
                })

        # 按修改时间排序
        backups.sort(key=lambda x: x['mtime'], reverse=True)

        print(f"📁 找到 {len(backups)} 个备份文件:")
        for backup in backups:
            import datetime
            mtime = datetime.datetime.fromtimestamp(backup['mtime'])
            print(f"  📄 {backup['filename']} ({backup['size']} bytes, {mtime.strftime('%Y-%m-%d %H:%M:%S')})")

        return backups

    def export_all_formats(self, threshold, preset_name=None, stats=None):
        """导出所有格式的参数"""
        print("\n🚀 开始导出参数到所有格式...")

        # 控制台输出
        self.export_params_console(threshold, preset_name, stats)

        # JSON格式
        json_success = self.export_params_json(threshold, "optimized_params.json", preset_name, stats)

        # 代码格式
        code_success = self.export_params_code(threshold, "optimized_params.py")

        print(f"\n📊 导出结果:")
        print(f"  控制台: ✅")
        print(f"  JSON格式: {'✅' if json_success else '❌'}")
        print(f"  代码格式: {'✅' if code_success else '❌'}")

        return json_success and code_success
