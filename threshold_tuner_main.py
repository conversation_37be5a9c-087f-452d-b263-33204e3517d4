#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
矩形检测阈值调参工具 - 主程序
完整的离线调参工具，支持实时阈值调整、可视化反馈和参数导出

使用方法:
    python threshold_tuner_main.py

功能特点:
- 实时阈值调整和可视化反馈
- 多种环境预设（明亮、暗光、精确等）
- 双画面/四画面显示模式
- 参数导出和代码自动更新
- 完善的备份和恢复机制
"""

import sys
import os
import traceback

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from maix import image, display, app, time, camera
    import cv2
    from threshold_tuner import ThresholdTuner
    from rect_config import RectDetectionParams
    MAIX_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  Maix模块导入失败: {e}")
    print("💡 这是正常的，如果您不在maix环境中运行")
    MAIX_AVAILABLE = False

def show_welcome():
    """显示欢迎信息和使用说明"""
    print("\n" + "="*60)
    print("🎯 矩形检测阈值调参工具")
    print("="*60)
    print("📋 功能特点:")
    print("  • 实时阈值调整 (0-255)")
    print("  • 双画面/四画面可视化显示")
    print("  • 4种环境预设 + 自定义预设")
    print("  • 多格式参数导出 (控制台/JSON/代码)")
    print("  • 安全的代码自动更新")
    print("  • 完善的备份恢复机制")
    print("\n📖 快捷键说明:")
    print("  调整: Q/W(±5) A/S(±1) Z/X(±10)")
    print("  预设: 1-4(系统预设) 5(列表) C(创建)")
    print("  功能: I(信息) E(导出) U(更新) B(备份)")
    print("  显示: T(切换视图) R(重置) ESC(退出)")
    print("="*60)

def show_system_check():
    """显示系统检查信息"""
    print("\n🔍 系统检查:")
    
    # 检查maix环境
    if MAIX_AVAILABLE:
        print("  ✅ Maix环境: 可用")
    else:
        print("  ❌ Maix环境: 不可用 (仅支持模拟模式)")
    
    # 检查OpenCV
    try:
        import cv2
        print(f"  ✅ OpenCV: {cv2.__version__}")
    except ImportError:
        print("  ❌ OpenCV: 未安装")
        return False
    
    # 检查配置文件
    try:
        from rect_config import RectDetectionParams
        print("  ✅ 配置系统: 可用")
    except ImportError:
        print("  ❌ 配置系统: 不可用")
        return False
    
    # 检查调参工具
    try:
        from threshold_tuner import ThresholdTuner
        print("  ✅ 调参工具: 可用")
    except ImportError:
        print("  ❌ 调参工具: 不可用")
        return False
    
    return True

def get_keyboard_input():
    """获取键盘输入 (跨平台兼容)"""
    try:
        # Windows
        if os.name == 'nt':
            import msvcrt
            if msvcrt.kbhit():
                key = msvcrt.getch().decode('utf-8', errors='ignore')
                return key
        # Unix/Linux/Mac
        else:
            import select
            import tty
            import termios
            
            if select.select([sys.stdin], [], [], 0) == ([sys.stdin], [], []):
                old_settings = termios.tcgetattr(sys.stdin)
                try:
                    tty.cbreak(sys.stdin.fileno())
                    key = sys.stdin.read(1)
                    return key
                finally:
                    termios.tcsetattr(sys.stdin, termios.TCSADRAIN, old_settings)
    except:
        pass
    
    return None

def run_simulation_mode():
    """运行模拟模式 (无maix环境)"""
    print("\n🔧 启动模拟模式...")
    print("💡 在此模式下，您可以测试所有功能，但无法看到实际图像")
    print("📝 所有参数调整和导出功能都可以正常使用")
    
    # 创建模拟的调参器
    class SimulationTuner:
        def __init__(self):
            from threshold_tuner import ThresholdTuner
            # 只初始化参数，不初始化摄像头和显示器
            self.current_threshold = 60
            self.current_preset = "default"
            self.white_pixels = 0
            self.total_pixels = 0
            self.white_percentage = 0.0
            
            # 初始化管理器
            from threshold_tuner import PresetManager, CodeUpdater
            self.preset_manager = PresetManager()
            self.code_updater = CodeUpdater()
            
            print("✅ 模拟调参器初始化完成")
        
        def handle_key_input(self, key_char):
            """处理键盘输入"""
            if key_char == 'q':
                self.current_threshold = min(255, self.current_threshold + 5)
                print(f"阈值调整为: {self.current_threshold}")
            elif key_char == 'w':
                self.current_threshold = max(0, self.current_threshold - 5)
                print(f"阈值调整为: {self.current_threshold}")
            elif key_char == 'a':
                self.current_threshold = min(255, self.current_threshold + 1)
                print(f"阈值调整为: {self.current_threshold}")
            elif key_char == 's':
                self.current_threshold = max(0, self.current_threshold - 1)
                print(f"阈值调整为: {self.current_threshold}")
            elif key_char == 'z':
                self.current_threshold = min(255, self.current_threshold + 10)
                print(f"阈值调整为: {self.current_threshold}")
            elif key_char == 'x':
                self.current_threshold = max(0, self.current_threshold - 10)
                print(f"阈值调整为: {self.current_threshold}")
            elif key_char == '1':
                self.preset_manager.apply_preset("default", self)
            elif key_char == '2':
                self.preset_manager.apply_preset("bright", self)
            elif key_char == '3':
                self.preset_manager.apply_preset("dark", self)
            elif key_char == '4':
                self.preset_manager.apply_preset("precise", self)
            elif key_char == '5':
                self.preset_manager.list_all_presets()
            elif key_char == 'c':
                preset_name = f"custom_{self.current_threshold}"
                self.preset_manager.create_preset_from_current(preset_name, self, f"自定义预设-阈值{self.current_threshold}")
            elif key_char == 'i':
                info = self.preset_manager.get_preset_info(self.current_preset)
                if info:
                    print(f"当前预设: {info['name']} - {info['description']}")
                else:
                    print("当前未使用预设")
            elif key_char == 'e':
                stats = {"white_pixels": 12345, "white_percentage": 15.6}
                self.code_updater.export_all_formats(self.current_threshold, self.current_preset, stats)
            elif key_char == 'u':
                print("准备更新main.py中的阈值参数...")
                self.code_updater.update_code_file("main.py", self.current_threshold, confirm=True)
            elif key_char == 'b':
                self.code_updater.list_backups()
            elif key_char == 'r':
                self.current_threshold = 60
                self.current_preset = "default"
                print("重置到默认值: 60")
            elif key_char == '\x1b' or key_char == 'q':  # ESC或q退出
                return False
            
            return True
    
    # 运行模拟模式
    tuner = SimulationTuner()
    
    print("\n🚀 模拟模式运行中...")
    print("💡 按任意快捷键测试功能，按ESC退出")
    
    try:
        while True:
            key = get_keyboard_input()
            if key:
                if not tuner.handle_key_input(key):
                    break
            
            # 避免CPU占用过高
            import time
            time.sleep(0.1)
    
    except KeyboardInterrupt:
        print("\n👋 用户中断，退出模拟模式")
    
    print("✅ 模拟模式结束")

def run_full_mode():
    """运行完整模式 (maix环境)"""
    print("\n🚀 启动完整模式...")
    
    try:
        # 初始化调参工具
        tuner = ThresholdTuner()
        
        print("✅ 调参工具初始化完成")
        print("🎥 摄像头启动中...")
        
        # 主循环
        frame_count = 0
        while not app.need_exit():
            try:
                # 读取图像
                img = tuner.cam.read()
                img = image.image2cv(img, ensure_bgr=True, copy=False)
                
                # 处理图像并显示
                display_img, binary_img, detection_img = tuner.process_and_display(img)
                
                # 转换回maix格式并显示
                img_show = image.cv2image(display_img, bgr=True, copy=False)
                tuner.disp.show(img_show)
                
                # 处理键盘输入 (这里需要根据实际maix平台的输入方式调整)
                # 由于maix平台的键盘输入方式可能不同，这里提供一个框架
                key = get_keyboard_input()
                if key:
                    if not tuner.handle_key_input(key):
                        break
                
                frame_count += 1
                
                # 控制帧率
                time.sleep_ms(50)
                
            except Exception as e:
                print(f"❌ 处理帧时出错: {e}")
                traceback.print_exc()
                continue
    
    except Exception as e:
        print(f"❌ 完整模式运行失败: {e}")
        traceback.print_exc()
        return False
    
    finally:
        # 清理资源
        if 'tuner' in locals():
            tuner.cleanup()
    
    print("✅ 完整模式结束")
    return True

def main():
    """主函数"""
    try:
        # 显示欢迎信息
        show_welcome()
        
        # 系统检查
        if not show_system_check():
            print("\n❌ 系统检查失败，无法启动")
            return 1
        
        # 根据环境选择运行模式
        if MAIX_AVAILABLE:
            print("\n🎯 检测到Maix环境，启动完整模式")
            success = run_full_mode()
        else:
            print("\n🔧 未检测到Maix环境，启动模拟模式")
            success = run_simulation_mode()
        
        if success:
            print("\n✅ 程序正常结束")
            return 0
        else:
            print("\n❌ 程序异常结束")
            return 1
    
    except KeyboardInterrupt:
        print("\n👋 用户中断，程序退出")
        return 0
    
    except Exception as e:
        print(f"\n💥 程序发生未处理的异常: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
