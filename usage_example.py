#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调参工具使用示例

这个示例展示了如何使用调参工具的各种功能
"""

def show_usage_examples():
    """显示使用示例"""
    print("🎯 矩形检测阈值调参工具 - 使用示例")
    print("="*60)
    
    print("\n📋 1. 基本使用流程:")
    print("   1️⃣  启动工具: python start_tuner.py")
    print("   2️⃣  调整阈值: 使用 Q/W/A/S/Z/X 键")
    print("   3️⃣  观察效果: 实时查看二值化结果")
    print("   4️⃣  应用预设: 按 1-4 键快速切换环境")
    print("   5️⃣  导出参数: 按 E 键导出最优参数")
    print("   6️⃣  更新代码: 按 U 键自动更新main.py")
    
    print("\n🎮 2. 快捷键参考:")
    print("   阈值调整:")
    print("     Q/W: ±5    A/S: ±1    Z/X: ±10")
    print("   ")
    print("   预设管理:")
    print("     1: 默认    2: 明亮    3: 暗光    4: 精确")
    print("     5: 列表    C: 创建    I: 信息")
    print("   ")
    print("   功能操作:")
    print("     E: 导出    U: 更新    B: 备份")
    print("     T: 切换视图    R: 重置    ESC: 退出")
    
    print("\n🌟 3. 使用场景示例:")
    
    print("\n   场景1: 明亮环境调参")
    print("   ├─ 按 '2' 应用明亮环境预设")
    print("   ├─ 使用 Q/W 微调阈值")
    print("   ├─ 按 'T' 切换到四画面查看详细效果")
    print("   └─ 按 'E' 导出最终参数")
    
    print("\n   场景2: 暗光环境调参")
    print("   ├─ 按 '3' 应用暗光环境预设")
    print("   ├─ 使用 A/S 精细调整")
    print("   ├─ 按 'C' 保存为自定义预设")
    print("   └─ 按 'U' 直接更新到代码")
    
    print("\n   场景3: 精确检测调参")
    print("   ├─ 按 '4' 应用精确检测预设")
    print("   ├─ 使用 Z/X 大幅调整找到范围")
    print("   ├─ 使用 Q/W 精确定位最佳值")
    print("   └─ 按 'I' 查看当前预设信息")
    
    print("\n💡 4. 高级功能:")
    print("   • 自定义预设: 调整到满意参数后按 'C' 创建")
    print("   • 备份管理: 按 'B' 查看所有备份文件")
    print("   • 批量导出: 按 'E' 同时导出多种格式")
    print("   • 安全更新: 自动备份原文件再更新")
    
    print("\n🔧 5. 故障排除:")
    print("   问题: 看不到图像")
    print("   解决: 检查摄像头连接，或使用模拟模式")
    print("   ")
    print("   问题: 阈值调整无效果")
    print("   解决: 检查光照条件，尝试不同预设")
    print("   ")
    print("   问题: 代码更新失败")
    print("   解决: 检查文件权限，查看备份文件")
    
    print("\n📁 6. 文件说明:")
    print("   threshold_tuner_main.py  - 主程序")
    print("   start_tuner.py          - 快速启动")
    print("   check_setup.py          - 环境检查")
    print("   rect_config.py          - 配置管理")
    print("   threshold_tuner.py      - 核心功能")
    
    print("\n" + "="*60)
    print("🎉 开始使用: python start_tuner.py")
    print("📖 详细文档: 查看 README_threshold_tuner.md")

def show_code_integration_example():
    """显示代码集成示例"""
    print("\n🔗 代码集成示例:")
    print("="*40)
    
    print("\n原始代码:")
    print("```python")
    print("# 原始的固定阈值")
    print("_, binary = cv2.threshold(gray, 60, 255, cv2.THRESH_BINARY)")
    print("```")
    
    print("\n调参后代码:")
    print("```python")
    print("# 调参工具优化后的阈值")
    print("_, binary = cv2.threshold(gray, 75, 255, cv2.THRESH_BINARY)")
    print("```")
    
    print("\n高级集成:")
    print("```python")
    print("# 使用配置文件的方式")
    print("from rect_config import RectDetectionParams")
    print("threshold = RectDetectionParams.BINARY_THRESHOLD")
    print("_, binary = cv2.threshold(gray, threshold, 255, cv2.THRESH_BINARY)")
    print("```")

def main():
    """主函数"""
    show_usage_examples()
    show_code_integration_example()
    
    print("\n💡 提示: 这只是使用说明，要实际使用请运行:")
    print("   python start_tuner.py")

if __name__ == "__main__":
    main()
