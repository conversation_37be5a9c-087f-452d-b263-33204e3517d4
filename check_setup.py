#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调参工具安装检查和修复脚本

检查所有必需的文件和依赖，并提供修复建议。
"""

import os
import sys
import importlib

def check_files():
    """检查必需的文件"""
    print("📁 检查文件完整性...")
    
    required_files = [
        "threshold_tuner.py",
        "rect_config.py", 
        "threshold_tuner_main.py",
        "start_tuner.py"
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file} (缺失)")
            missing_files.append(file)
    
    return len(missing_files) == 0, missing_files

def check_dependencies():
    """检查Python依赖"""
    print("\n📦 检查Python依赖...")
    
    dependencies = {
        "cv2": "OpenCV (图像处理)",
        "numpy": "NumPy (数值计算)", 
        "json": "JSON (内置模块)",
        "re": "正则表达式 (内置模块)",
        "os": "操作系统接口 (内置模块)",
        "sys": "系统接口 (内置模块)"
    }
    
    missing_deps = []
    for module, description in dependencies.items():
        try:
            importlib.import_module(module)
            print(f"  ✅ {module} - {description}")
        except ImportError:
            print(f"  ❌ {module} - {description} (未安装)")
            missing_deps.append(module)
    
    # 检查可选依赖
    print("\n🔧 检查可选依赖 (Maix环境)...")
    try:
        importlib.import_module("maix")
        print("  ✅ maix - Maix平台支持 (完整功能)")
    except ImportError:
        print("  ⚠️  maix - Maix平台支持 (仅模拟模式)")
    
    return len(missing_deps) == 0, missing_deps

def check_permissions():
    """检查文件权限"""
    print("\n🔐 检查文件权限...")
    
    # 检查当前目录写权限
    try:
        test_file = "test_write_permission.tmp"
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        print("  ✅ 当前目录写权限")
    except Exception as e:
        print(f"  ❌ 当前目录写权限: {e}")
        return False
    
    # 检查备份目录创建权限
    try:
        backup_dir = "backups"
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
        print("  ✅ 备份目录创建权限")
    except Exception as e:
        print(f"  ❌ 备份目录创建权限: {e}")
        return False
    
    return True

def provide_fix_suggestions(missing_files, missing_deps):
    """提供修复建议"""
    if missing_files or missing_deps:
        print("\n🔧 修复建议:")
        
        if missing_files:
            print("\n📁 缺失文件修复:")
            for file in missing_files:
                print(f"  • 请确保 {file} 文件存在于当前目录")
            print("  💡 建议重新下载完整的工具包")
        
        if missing_deps:
            print("\n📦 缺失依赖修复:")
            for dep in missing_deps:
                if dep == "cv2":
                    print("  • 安装OpenCV: pip install opencv-python")
                elif dep == "numpy":
                    print("  • 安装NumPy: pip install numpy")
                else:
                    print(f"  • 安装 {dep}: pip install {dep}")
        
        return False
    
    return True

def run_basic_test():
    """运行基本功能测试"""
    print("\n🧪 运行基本功能测试...")
    
    try:
        # 测试配置系统
        from rect_config import RectDetectionParams
        config = RectDetectionParams.get_current_config()
        print("  ✅ 配置系统")
        
        # 测试JSON保存加载
        test_data = {"test": "value"}
        success = RectDetectionParams.save_config("test_config.json", test_data)
        if success:
            loaded_data = RectDetectionParams.load_config("test_config.json")
            if loaded_data and loaded_data.get("test") == "value":
                print("  ✅ JSON配置系统")
                os.remove("test_config.json")  # 清理测试文件
            else:
                print("  ❌ JSON配置系统 (加载失败)")
                return False
        else:
            print("  ❌ JSON配置系统 (保存失败)")
            return False
        
        # 测试调参工具类导入 (跳过需要maix的部分)
        try:
            from threshold_tuner import PresetManager, CodeUpdater, DisplayManager
            print("  ✅ 调参工具核心类")
        except ImportError as e:
            print(f"  ❌ 调参工具核心类导入失败: {e}")
            return False
        
        # 测试预设管理
        preset_manager = PresetManager()
        presets = preset_manager.get_all_presets()
        if len(presets) >= 4:  # 至少有4个系统预设
            print("  ✅ 预设管理系统")
        else:
            print("  ❌ 预设管理系统 (预设不足)")
            return False
        
        print("  ✅ 所有基本功能测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 功能测试失败: {e}")
        return False

def main():
    """主检查函数"""
    print("🔍 调参工具安装检查")
    print("="*50)
    
    # 检查文件
    files_ok, missing_files = check_files()
    
    # 检查依赖
    deps_ok, missing_deps = check_dependencies()
    
    # 检查权限
    perms_ok = check_permissions()
    
    # 提供修复建议
    fix_ok = provide_fix_suggestions(missing_files, missing_deps)
    
    # 运行基本测试
    if files_ok and deps_ok and perms_ok:
        test_ok = run_basic_test()
    else:
        test_ok = False
        print("\n⚠️  跳过功能测试 (存在基础问题)")
    
    # 总结
    print("\n" + "="*50)
    print("📊 检查结果总结:")
    print(f"  文件完整性: {'✅' if files_ok else '❌'}")
    print(f"  依赖安装: {'✅' if deps_ok else '❌'}")
    print(f"  文件权限: {'✅' if perms_ok else '❌'}")
    print(f"  功能测试: {'✅' if test_ok else '❌'}")
    
    if files_ok and deps_ok and perms_ok and test_ok:
        print("\n🎉 所有检查通过！调参工具已准备就绪")
        print("💡 您可以运行 'python start_tuner.py' 启动工具")
        return 0
    else:
        print("\n⚠️  发现问题，请根据上述建议进行修复")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        input("\n按回车键退出...")
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 检查过程中发生错误: {e}")
        input("按回车键退出...")
        sys.exit(1)
