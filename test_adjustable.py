from maix import image, display, app, time, camera
import cv2

# 初始化显示和摄像头
disp = display.Display()
cam = camera.Camera(640, 480, image.Format.FMT_BGR888)

# 可调整的参数配置
class RectDetectionConfig:
    def __init__(self):
        self.min_contour_area = 30000      # 最小轮廓面积
        self.max_contour_area = 250000     # 最大轮廓面积
        self.binary_threshold = 50         # 二值化阈值
        self.epsilon_factor = 0.03         # 轮廓近似精度
        self.target_sides = 4              # 目标边数
        
    def adjust_binary_threshold(self, delta):
        """调整二值化阈值"""
        self.binary_threshold = max(0, min(255, self.binary_threshold + delta))
        print(f"二值化阈值: {self.binary_threshold}")
        
    def adjust_epsilon(self, delta):
        """调整轮廓近似精度"""
        self.epsilon_factor = max(0.01, min(0.1, self.epsilon_factor + delta))
        print(f"轮廓近似精度: {self.epsilon_factor:.3f}")
        
    def adjust_area_range(self, min_delta, max_delta):
        """调整面积范围"""
        self.min_contour_area = max(1000, self.min_contour_area + min_delta)
        self.max_contour_area = max(self.min_contour_area + 1000, self.max_contour_area + max_delta)
        print(f"面积范围: {self.min_contour_area} - {self.max_contour_area}")

config = RectDetectionConfig()

# 显示当前参数
def show_params():
    print(f"\n当前参数:")
    print(f"二值化阈值: {config.binary_threshold}")
    print(f"轮廓近似精度: {config.epsilon_factor:.3f}")
    print(f"面积范围: {config.min_contour_area} - {config.max_contour_area}")
    print("调整方法:")
    print("按键盘 q/w 调整二值化阈值 ±5")
    print("按键盘 a/s 调整轮廓精度 ±0.005")
    print("按键盘 z/x 调整最小面积 ±5000")
    print("按键盘 c/v 调整最大面积 ±10000\n")

show_params()

while not app.need_exit():
    # 从摄像头读取图像
    img = cam.read()
    img = image.image2cv(img, ensure_bgr=True, copy=False)
    
    # 转灰度并二值化
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    _, binary = cv2.threshold(gray, config.binary_threshold, 255, cv2.THRESH_BINARY)

    # 查找轮廓
    contours, _ = cv2.findContours(binary, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)
    quads = []
    
    for cnt in contours:
        area = cv2.contourArea(cnt)
        
        if config.min_contour_area < area < config.max_contour_area:
            epsilon = config.epsilon_factor * cv2.arcLength(cnt, True)
            approx = cv2.approxPolyDP(cnt, epsilon, True)
            if len(approx) == config.target_sides:
                quads.append((approx, area))

    # 按面积降序排列
    quads.sort(key=lambda x: x[1], reverse=True)

    # 绘制轮廓和参数信息
    output = img.copy()
    for i, (approx, area) in enumerate(quads):
        color = (0, 0, 255) if i == 0 else (0, 255, 0)  # 红色为最大，绿色为其他
        cv2.drawContours(output, [approx], -1, color, 2)
        
        # 显示面积信息
        if len(approx) > 0:
            x, y = approx[0][0]
            cv2.putText(output, f"Area: {int(area)}", (x, y-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

    # 显示参数信息在图像上
    cv2.putText(output, f"Binary Threshold: {config.binary_threshold}", (10, 30), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    cv2.putText(output, f"Epsilon Factor: {config.epsilon_factor:.3f}", (10, 60), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    cv2.putText(output, f"Area Range: {config.min_contour_area}-{config.max_contour_area}", (10, 90), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    cv2.putText(output, f"Detected Quads: {len(quads)}", (10, 120), 
               cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

    # 转回 maix.image.Image 并显示
    img_show = image.cv2image(output, bgr=True, copy=False)
    disp.show(img_show)
    
    # 简单的键盘输入处理（需要根据实际平台调整）
    # 这里只是示例，实际使用时可能需要其他输入方式
    time.sleep_ms(50)
