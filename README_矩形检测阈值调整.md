# 矩形检测阈值调整指南

## 概述
本项目提供了一个基于OpenCV的矩形检测系统，支持实时参数调整以优化检测效果。

## 文件说明
- `test.py` - 基础矩形检测程序
- `test_adjustable.py` - 带实时参数调整的增强版本
- `rect_config.py` - 统一参数配置文件

## 主要阈值参数

### 1. 二值化阈值 (BINARY_THRESHOLD)
- **默认值**: 50
- **范围**: 0-255
- **作用**: 将灰度图像转换为黑白二值图像
- **调整原则**:
  - 矩形太暗或背景太亮 → 增加阈值 (60-80)
  - 矩形太亮或背景太暗 → 减少阈值 (30-40)
  - 建议步长: ±5

### 2. 轮廓面积阈值
- **最小面积 (MIN_CONTOUR_AREA)**: 30000
- **最大面积 (MAX_CONTOUR_AREA)**: 250000
- **作用**: 过滤掉过小或过大的轮廓
- **调整方法**:
  - 根据实际矩形大小调整范围
  - 可通过打印面积值确定合适范围
  - 最小面积步长: ±5000
  - 最大面积步长: ±10000

### 3. 轮廓近似精度 (EPSILON_FACTOR)
- **默认值**: 0.03 (3%)
- **范围**: 0.01-0.1
- **作用**: 控制轮廓近似的精确度
- **调整原则**:
  - 需要更精确轮廓 → 减少值 (0.01-0.02)
  - 需要更简化轮廓 → 增加值 (0.04-0.05)
  - 建议步长: ±0.005

## 使用方法

### 基础版本
```bash
python test.py
```

### 增强版本（推荐）
```bash
python test_adjustable.py
```

## 参数调整策略

### 场景优化参数
配置文件提供了针对不同场景的优化参数：

1. **明亮环境 (bright)**:
   - 二值化阈值: 80
   - 轮廓精度: 0.025
   - 面积范围: 25000-300000

2. **暗光环境 (dark)**:
   - 二值化阈值: 30
   - 轮廓精度: 0.035
   - 面积范围: 20000-200000

3. **精确检测 (precise)**:
   - 二值化阈值: 60
   - 轮廓精度: 0.015
   - 面积范围: 35000-250000

### 调整步骤
1. **观察检测效果**: 运行程序查看当前检测结果
2. **识别问题**: 
   - 检测不到矩形 → 降低阈值或扩大面积范围
   - 检测到太多噪声 → 提高阈值或缩小面积范围
   - 轮廓不准确 → 调整轮廓近似精度
3. **逐步调整**: 每次只调整一个参数，观察效果
4. **记录最佳参数**: 找到最佳效果后更新配置文件

## 常见问题及解决方案

### 问题1: 检测不到矩形
**可能原因**: 二值化阈值不合适
**解决方案**: 
- 观察二值化后的图像
- 如果矩形区域为黑色，降低阈值
- 如果矩形区域为白色但不完整，提高阈值

### 问题2: 检测到太多噪声
**可能原因**: 面积阈值范围太大
**解决方案**:
- 提高最小面积阈值
- 降低最大面积阈值
- 调整轮廓近似精度

### 问题3: 矩形轮廓不准确
**可能原因**: 轮廓近似精度不合适
**解决方案**:
- 需要更精确轮廓时降低epsilon_factor
- 需要更简化轮廓时提高epsilon_factor

## 高级调整技巧

### 1. 自适应阈值
对于光照不均匀的场景，可以使用自适应阈值：
```python
binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2)
```

### 2. 形态学操作
添加形态学操作改善二值化效果：
```python
kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
```

### 3. 多尺度检测
对不同大小的矩形使用不同的参数组合。

## 性能优化建议
1. 合理设置面积阈值范围，避免处理过多无效轮廓
2. 根据实际需求调整图像分辨率
3. 使用ROI（感兴趣区域）限制检测范围
4. 考虑使用GPU加速（如果可用）

## 更新日志
- 添加了统一的参数配置管理
- 支持不同场景的预设参数
- 提供了实时参数调整功能
- 增加了详细的调整指南和故障排除方法
